<style>
  /* Hide header and footer on activate page */
  /* .shopify-section--header,
  .shopify-section--footer,
  .header,
  .footer {
    display: none !important;
  } */

  .custom-bg-color-light-nonhover {
    background-color: ##e7e7e5 !important;
  }

  /* Force heading font for the specific activate page title */
  .js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd,
  h1.js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd {
    font-family: 'FormaDJRDisplay', 'FormaDJRDisplay-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
    font-weight: 650 !important; /* Use available ExtraBold weight instead of 600 */
    font-display: swap !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    text-rendering: optimizeLegibility !important;
  }

  /* Preload the specific font weight needed */
  @font-face {
    font-family: 'FormaDJRDisplay';
    src: url('{{ "FormaDJRDisplay-ExtraBold.woff2" | asset_url }}') format('woff2');
    font-weight: 650;
    font-style: normal;
    font-display: swap;
  }

  /* Safari-specific font loading fix */
  @supports (-webkit-appearance: none) {
    .js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd,
    h1.js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd {
      font-family: 'FormaDJRDisplay', -webkit-system-font, -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      font-weight: 650 !important;
    }
  }

  /* Force font loading for YouForm content */
  [data-youform-embed] h1,
  [data-youform-embed] .js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd {
    font-family: 'FormaDJRDisplay', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
    font-weight: 650 !important;
    font-synthesis: none !important;
  }

  /* Ensure font loads immediately for this element */
  .js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd {
    font-synthesis: none !important;
    text-rendering: optimizeLegibility !important;
  }

  /* Ensure full viewport height for the form */
  body {
    margin: 0;
    padding: 0;
  }

  /* Remove any top spacing that might be caused by hidden header */
  main[role="main"] {
    padding-top: 0 !important;
    margin-top: 0 !important;
  }
</style>

<div data-youform-embed data-form='bdzggzys' data-width='100%' data-height='700'></div>

<script src="https://app.youform.com/embed.js"></script>

<script>
  // Force font loading for YouForm content
  function forceFormFontLoading() {
    // Wait for YouForm to load
    const checkForForm = setInterval(() => {
      const formTitle = document.querySelector('.js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd');
      if (formTitle) {
        clearInterval(checkForForm);

        // Force the font family
        formTitle.style.fontFamily = "'FormaDJRDisplay', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif";
        formTitle.style.fontWeight = "650";
        formTitle.style.fontDisplay = "swap";

        // Force a reflow to trigger font loading
        formTitle.offsetHeight;

        // Try to load the font explicitly
        if ('fonts' in document) {
          document.fonts.load('650 1em "FormaDJRDisplay"').then(() => {
            console.log('FormaDJRDisplay font loaded successfully');
            formTitle.style.fontFamily = "'FormaDJRDisplay', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif";
          }).catch((error) => {
            console.warn('Font loading failed:', error);
          });
        }
      }
    }, 100);

    // Stop checking after 10 seconds
    setTimeout(() => {
      clearInterval(checkForForm);
    }, 10000);
  }

  // Start checking when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', forceFormFontLoading);
  } else {
    forceFormFontLoading();
  }

  // Also check when YouForm script loads
  window.addEventListener('load', forceFormFontLoading);
</script>
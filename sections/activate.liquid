<style>
  /* Hide header and footer on activate page */
  /* .shopify-section--header,
  .shopify-section--footer,
  .header,
  .footer {
    display: none !important;
  } */

  .custom-bg-color-light-nonhover {
    background-color: ##e7e7e5 !important;
  }

  /* Force heading font for the specific activate page title */
  .js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd,
  h1.js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd {
    font-family: 'FormaDJRDisplay', 'FormaDJRDisplay-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
    font-weight: 600 !important;
    font-display: swap !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
  }

  /* Safari-specific font loading fix */
  @supports (-webkit-appearance: none) {
    .js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd,
    h1.js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd {
      font-family: var(--heading-font-family) !important;
      font-weight: var(--heading-font-weight, 600) !important;
    }
  }

  /* Ensure font loads immediately for this element */
  .js_block_title_7703e002-18f4-4cda-922c-6492e0c62ffd {
    font-synthesis: none !important;
    text-rendering: optimizeLegibility !important;
  }

  /* Ensure full viewport height for the form */
  body {
    margin: 0;
    padding: 0;
  }

  /* Remove any top spacing that might be caused by hidden header */
  main[role="main"] {
    padding-top: 0 !important;
    margin-top: 0 !important;
  }
</style>

<div data-youform-embed data-form='bdzggzys' data-width='100%' data-height='700'></div>

<script src="https://app.youform.com/embed.js"></script>
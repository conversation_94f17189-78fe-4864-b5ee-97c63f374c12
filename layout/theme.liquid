{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
NOTE TO DEVELOPERS: welcome to Impact theme! We hope that you will enjoy editing this theme as much as we did for
  developing it. We have put a lot of work to make this theme as developer friendly as possible by offering you
  hooks to integrate into critical parts of the theme. You will find the complete technical documentation (including
  all events, dependencies...) in the "documentation.txt" file, located in the Assets folder.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<!doctype html>

<html lang="{{ request.locale.iso_code }}" dir="{% render 'direction' %}" class="fonts-loading">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="theme-color" content="{{ settings.header_background }}">

    {%- comment -%}Preconnect to required origins{%- endcomment -%}
    <link rel="preconnect" href="https://cdn.shopify.com">
    <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    <link rel="dns-prefetch" href="https://productreviews.shopifycdn.com">

    {%- comment -%}Preload critical font files{%- endcomment -%}

    {%- comment -%}FormaDJRBanner{%- endcomment -%}
    <link rel="preload" href="{{ 'FormaDJRBanner-Regular.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRBanner-Bold.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRBanner-Black.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">

    {%- comment -%}FormaDJRDisplay{%- endcomment -%}
    <link rel="preload" href="{{ 'FormaDJRDisplay-Regular.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRDisplay-Bold.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRDisplay-Black.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">

    {%- comment -%}FormaDJRText{%- endcomment -%}
    <link rel="preload" href="{{ 'FormaDJRText-Regular.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRText-Bold.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRText-Black.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">

    {%- comment -%}FormaDJRDeck{%- endcomment -%}
    <link rel="preload" href="{{ 'FormaDJRDeck-Regular.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRDeck-Bold.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRDeck-Black.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">

    {%- comment -%}FormaDJRMicro{%- endcomment -%}
    <link rel="preload" href="{{ 'FormaDJRMicro-Regular.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRMicro-Bold.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'FormaDJRMicro-Black.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">

    {%- comment -%}Meltmino{%- endcomment -%}
    <link rel="preload" href="{{ 'Meltmino-Regular.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">
    <link rel="preload" href="{{ 'Meltmino-Bold.woff2' | asset_url }}" as="font" type="font/woff2" crossorigin fetchpriority="high">

    {%- unless settings.heading_font.system? -%}
      <link rel="preload" href="{{ settings.heading_font | font_url }}" as="font" type="font/woff2" crossorigin>
    {%- endunless -%}

    {%- unless settings.text_font.system? -%}
      <link rel="preload" href="{{ settings.text_font | font_url }}" as="font" type="font/woff2" crossorigin>
    {%- endunless -%}

    {%- comment -%}Preload critical CSS{%- endcomment -%}
    <link rel="preload" href="{{ 'font-size-adjust.css' | asset_url }}" as="style">
    <link rel="preload" href="{{ 'custom_fonts_local.css' | asset_url }}" as="style">
    <link rel="preload" href="{{ 'custom_fonts.css' | asset_url }}" as="style">
    <link rel="preload" href="{{ 'theme.css' | asset_url }}" as="style">
    <link rel="preload" href="{{ 'theme2.css' | asset_url }}" as="style">
    <link rel="preload" href="{{ 'theme3.css' | asset_url }}" as="style">

    {%- comment -%}Preload hero image for faster loading on homepage{%- endcomment -%}
    {%- if template.name == 'index' -%}
      {%- comment -%}Preload the main hero image from the homepage{%- endcomment -%}
      <link rel="preload" href="{{ 'sticks_b2457064-631c-4735-b6b8-c5513a4bac4a.png' | file_img_url: '1200x' }}" as="image" fetchpriority="high">
      {%- comment -%}Also preload a smaller version for mobile{%- endcomment -%}
      <link rel="preload" href="{{ 'sticks_b2457064-631c-4735-b6b8-c5513a4bac4a.png' | file_img_url: '800x' }}" as="image" media="(max-width: 740px)">
    {%- endif -%}

    {%- comment -%}Load critical CSS variables and font declarations first{%- endcomment -%}
    {%- render 'css-variables' -%}

    {%- comment -%}Load font size adjustment first to prevent layout shifts{%- endcomment -%}
    <link rel="stylesheet" href="{{ 'font-size-adjust.css' | asset_url }}">

    {%- comment -%}Load font stylesheets{%- endcomment -%}
    <link rel="stylesheet" href="{{ 'custom_fonts_local.css' | asset_url }}" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="{{ 'custom_fonts.css' | asset_url }}" media="print" onload="this.media='all'">
    <noscript>
      <link rel="stylesheet" href="{{ 'custom_fonts_local.css' | asset_url }}">
      <link rel="stylesheet" href="{{ 'custom_fonts.css' | asset_url }}">
    </noscript>

    {%- comment -%}Load theme CSS{%- endcomment -%}
    {{- 'theme.css' | asset_url | stylesheet_tag: preload: true -}}
    {{- 'theme2.css' | asset_url | stylesheet_tag: preload: true -}}
    {{- 'theme3.css' | asset_url | stylesheet_tag: preload: true -}}
    {{- 'variant-picker-custom.css' | asset_url | stylesheet_tag -}}
    {{- 'flavor-info-dots.css' | asset_url | stylesheet_tag -}}
    {{- 'appstle-custom.css' | asset_url | stylesheet_tag -}}
    {{- 'electrolyte-info.css' | asset_url | stylesheet_tag -}}
    {{- 'product-title-custom.css' | asset_url | stylesheet_tag -}}
    {{- 'judge-me-custom.css' | asset_url | stylesheet_tag -}}
    {{- 'slider-whats-inside.css' | asset_url | stylesheet_tag -}}
    {{- 'section-spacing-responsive.css' | asset_url | stylesheet_tag -}}
    {{- 'judge-me-mobile-spacing.css' | asset_url | stylesheet_tag -}}
    {{- 'extreme-mobile-spacing.css' | asset_url | stylesheet_tag -}}
    {{- 'product-gallery-fix.css' | asset_url | stylesheet_tag -}}
    {{- 'cart-drawer-custom.css' | asset_url | stylesheet_tag -}}
    {{- 'mobile-navigation-custom.css' | asset_url | stylesheet_tag -}}
    {{- 'footer-custom.css' | asset_url | stylesheet_tag -}}
    {{- 'hero-image-optimization.css' | asset_url | stylesheet_tag -}}

    <title>{% if page_title == blank %}{{ shop.name }}{% else %}{{ page_title }}{% if current_page != 1 %} &ndash; {{ 'general.page' | t: page: current_page }}{% endif %}{% endif %}</title>

    {%- if page_description -%}
      <meta name="description" content="{{ page_description | escape }}">
    {%- endif -%}

    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon -%}
      <link rel="shortcut icon" href="{{ settings.favicon | image_url: width: 96 }}">
      <link rel="apple-touch-icon" href="{{ settings.favicon | image_url: width: 180 }}">
    {%- endif -%}

    {%- render 'social-meta-tags' -%}
    {%- render 'microdata-schema' -%}
    {%- render 'js-variables' -%}

    <script>
      if (!(HTMLScriptElement.supports && HTMLScriptElement.supports('importmap'))) {
        const importMapPolyfill = document.createElement('script');
        importMapPolyfill.async = true;
        importMapPolyfill.src = "{{ 'es-module-shims.min.js' | asset_url }}";

        document.head.appendChild(importMapPolyfill);
      }
    </script>

    <script type="importmap">
      {%- comment -%}On Safari 16.3 and lower, a polyfill is used to load importmap{%- endcomment -%}
      {
        "imports": {
          "vendor": "{{ 'vendor.min.js' | asset_url }}",
          "theme": "{{ 'theme.js' | asset_url }}",
          "photoswipe": "{{ 'photoswipe.min.js' | asset_url }}"
        }
      }
    </script>

    <script src="{{ 'font-loading.js' | asset_url }}" defer></script>
    <script type="module" src="{{ 'vendor.min.js' | asset_url }}"></script>
    <script type="module" src="{{ 'theme.js' | asset_url }}"></script>
    <script src="{{ 'appstle-init.js' | asset_url }}" defer></script>
    <script src="{{ 'flavor-info-dots.js' | asset_url }}" defer></script>
    <script src="{{ 'judge-me-title-fix.js' | asset_url }}" defer></script>
    <script src="{{ 'product-gallery-fix.js' | asset_url }}" defer></script>
    <script src="{{ 'cart-drawer-animation.js' | asset_url }}" defer></script>
    <script src="{{ 'mobile-menu-animation.js' | asset_url }}" defer></script>

    {{ content_for_header }}
  </head>

  <body class="{% if settings.zoom_image_on_hover %}zoom-image--enabled{% endif %}">
    {%- unless template.name == 'page.activate' -%}
      {%- render 'site-loader' -%}
    {%- endunless -%}
    {%- render 'shadow-dom-templates' -%}

    <a href="#main" class="skip-to-content sr-only">{{ 'general.accessibility.skip_to_content' | t }}</a>

    {%- if request.page_type != 'password' -%}
      {%- sections 'header-group' -%}
      {%- sections 'overlay-group' -%}

      {%- if settings.cart_type == 'popover' -%}
        <cart-notification-drawer open-from="bottom" class="quick-buy-drawer drawer"></cart-notification-drawer>
      {%- endif -%}
    {%- endif -%}

    {%- if request.page_type == 'customers/account' or request.page_type == 'customers/order' or request.page_type == 'customers/addresses' -%}
      {%- section 'account-banner' -%}
    {%- endif -%}

    <main role="main" id="main" class="anchor">
      {{ content_for_layout }}

      {%- comment -%}
      IMPLEMENTATION NOTE: due to the very complex logic of margin/padding collapsing in Impact, the footer group is
      added into the main element to ensure that dynamic sections added into the footer group are properly laid out.
      {%- endcomment -%}
      {%- if request.page_type != 'password' -%}
        {%- sections 'footer-group' -%}
      {%- endif -%}
    </main>

    <script async type='text/javascript' src='https://static.klaviyo.com/onsite/js/klaviyo.js?company_id=ThHpUw'></script>
  </body>
</html>
